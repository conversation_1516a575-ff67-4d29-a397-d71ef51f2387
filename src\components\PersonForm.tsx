import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent, CardFooter } from '@/components/ui/card';
import { Person } from '../services/database';

interface PersonFormProps {
  person?: Person;
  onSubmit: (personData: Omit<Person, 'id'>) => Promise<void>;
  onCancel: () => void;
  isEditing?: boolean;
}

export const PersonForm: React.FC<PersonFormProps> = ({
  person,
  onSubmit,
  onCancel,
  isEditing = false
}) => {
  const [name, setName] = useState('');
  const [age, setAge] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ name?: string; age?: string }>({});

  // Initialize form with person data if editing
  useEffect(() => {
    if (person) {
      setName(person.name);
      setAge(person.age.toString());
    }
  }, [person]);

  const validateForm = (): boolean => {
    const newErrors: { name?: string; age?: string } = {};

    if (!name.trim()) {
      newErrors.name = 'Name is required';
    } else if (name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    const ageNum = parseInt(age);
    if (!age.trim()) {
      newErrors.age = 'Age is required';
    } else if (isNaN(ageNum)) {
      newErrors.age = 'Age must be a valid number';
    } else if (ageNum < 0) {
      newErrors.age = 'Age cannot be negative';
    } else if (ageNum > 150) {
      newErrors.age = 'Age cannot be greater than 150';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit({
        name: name.trim(),
        age: parseInt(age)
      });
      
      // Reset form if not editing
      if (!isEditing) {
        setName('');
        setAge('');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setName('');
    setAge('');
    setErrors({});
    onCancel();
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>
          {isEditing ? 'Edit Person' : 'Add New Person'}
        </CardTitle>
      </CardHeader>
      
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium">
              Name
            </label>
            <Input
              id="name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter person's name"
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="age" className="text-sm font-medium">
              Age
            </label>
            <Input
              id="age"
              type="number"
              value={age}
              onChange={(e) => setAge(e.target.value)}
              placeholder="Enter person's age"
              min="0"
              max="150"
              className={errors.age ? 'border-red-500' : ''}
            />
            {errors.age && (
              <p className="text-sm text-red-500">{errors.age}</p>
            )}
          </div>
        </CardContent>

        <CardFooter className="flex gap-2">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex-1"
          >
            {isSubmitting ? 'Saving...' : (isEditing ? 'Update' : 'Add Person')}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};
