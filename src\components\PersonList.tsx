import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Person } from '../services/database';

interface PersonListProps {
  persons: Person[];
  onEdit: (person: Person) => void;
  onDelete: (id: number) => void;
  loading?: boolean;
}

export const PersonList: React.FC<PersonListProps> = ({
  persons,
  onEdit,
  onDelete,
  loading = false
}) => {
  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="text-muted-foreground">Loading persons...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (persons.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <p className="text-lg mb-2">No persons found</p>
            <p className="text-sm">Add your first person using the form above.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Persons List ({persons.length})</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {persons.map((person) => (
            <PersonCard
              key={person.id}
              person={person}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

interface PersonCardProps {
  person: Person;
  onEdit: (person: Person) => void;
  onDelete: (id: number) => void;
}

const PersonCard: React.FC<PersonCardProps> = ({ person, onEdit, onDelete }) => {
  const handleDelete = () => {
    if (window.confirm(`Are you sure you want to delete ${person.name}?`)) {
      onDelete(person.id!);
    }
  };

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg bg-card">
      <div className="flex-1">
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <h3 className="font-semibold text-lg">{person.name}</h3>
            <p className="text-muted-foreground">
              Age: {person.age} years old
            </p>
          </div>
          <div className="text-sm text-muted-foreground">
            ID: {person.id}
          </div>
        </div>
      </div>
      
      <div className="flex gap-2 ml-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onEdit(person)}
        >
          Edit
        </Button>
        <Button
          variant="destructive"
          size="sm"
          onClick={handleDelete}
        >
          Delete
        </Button>
      </div>
    </div>
  );
};
