import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { PersonForm } from './PersonForm';
import { PersonList } from './PersonList';
import { usePersons } from '../hooks/usePersons';
import { Person } from '../services/database';

export const PersonManager: React.FC = () => {
  const {
    persons,
    loading,
    error,
    createPerson,
    updatePerson,
    deletePerson,
    clearError
  } = usePersons();

  const [showForm, setShowForm] = useState(false);
  const [editingPerson, setEditingPerson] = useState<Person | null>(null);

  const handleCreatePerson = async (personData: Omit<Person, 'id'>) => {
    try {
      await createPerson(personData);
      setShowForm(false);
    } catch (error) {
      // Error is handled by the hook
    }
  };

  const handleUpdatePerson = async (personData: Omit<Person, 'id'>) => {
    if (!editingPerson?.id) return;
    
    try {
      await update<PERSON>erson(editingPerson.id, personData);
      setEditingPerson(null);
    } catch (error) {
      // Error is handled by the hook
    }
  };

  const handleEditPerson = (person: Person) => {
    setEditingPerson(person);
    setShowForm(false);
  };

  const handleDeletePerson = async (id: number) => {
    try {
      await deletePerson(id);
    } catch (error) {
      // Error is handled by the hook
    }
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setEditingPerson(null);
  };

  const handleAddNewPerson = () => {
    setEditingPerson(null);
    setShowForm(true);
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="text-3xl font-bold text-center">
              SQLite Person Manager
            </CardTitle>
            <p className="text-center text-muted-foreground">
              A complete CRUD application using SQLite in the browser
            </p>
          </CardHeader>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="text-red-800">
                  <strong>Error:</strong> {error}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearError}
                  className="text-red-800 border-red-300"
                >
                  Dismiss
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex gap-4 justify-center">
          <Button
            onClick={handleAddNewPerson}
            disabled={loading}
            className="px-6"
          >
            Add New Person
          </Button>
          
          {persons.length > 0 && (
            <div className="text-sm text-muted-foreground flex items-center">
              Total: {persons.length} person{persons.length !== 1 ? 's' : ''}
            </div>
          )}
        </div>

        {/* Form Section */}
        <div className="flex justify-center">
          {(showForm || editingPerson) && (
            <PersonForm
              person={editingPerson || undefined}
              onSubmit={editingPerson ? handleUpdatePerson : handleCreatePerson}
              onCancel={handleCancelForm}
              isEditing={!!editingPerson}
            />
          )}
        </div>

        {/* Persons List */}
        <PersonList
          persons={persons}
          onEdit={handleEditPerson}
          onDelete={handleDeletePerson}
          loading={loading}
        />

        {/* Database Info */}
        <Card>
          <CardContent className="p-4">
            <div className="text-center text-sm text-muted-foreground">
              <p>
                This application uses SQLite compiled to WebAssembly (SQL.js) 
                to demonstrate CRUD operations in the browser.
              </p>
              <p className="mt-2">
                Data is stored in memory and will be lost when you refresh the page.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
