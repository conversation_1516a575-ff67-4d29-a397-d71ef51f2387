import { useState, useEffect, useCallback } from "react";
import { databaseService, Person } from "../services/database";

export const usePersons = () => {
  const [persons, setPersons] = useState<Person[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize database and load persons
  const initializeAndLoad = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      await databaseService.initialize();
      const allPersons = await databaseService.getAllPersons();
      setPersons(allPersons);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load persons");
      console.error("Error loading persons:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load persons from database
  const loadPersons = useCallback(async () => {
    try {
      setError(null);
      const allPersons = await databaseService.getAllPersons();
      setPersons(allPersons);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load persons");
      console.error("Error loading persons:", err);
    }
  }, []);

  // Create a new person
  const createPerson = useCallback(async (personData: Omit<Person, "id">) => {
    try {
      setError(null);
      const newPerson = await databaseService.createPerson(personData);
      setPersons((prev) => [...prev, newPerson]);
      return newPerson;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to create person";
      setError(errorMessage);
      console.error("Error creating person:", err);
      throw new Error(errorMessage);
    }
  }, []);

  // Update an existing person
  const updatePerson = useCallback(
    async (id: number, personData: Omit<Person, "id">) => {
      try {
        setError(null);

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(
            () => reject(new Error("Update operation timed out")),
            10000
          )
        );

        const updatePromise = databaseService.updatePerson(id, personData);
        const updatedPerson = (await Promise.race([
          updatePromise,
          timeoutPromise,
        ])) as Person | null;

        if (updatedPerson) {
          setPersons((prev) =>
            prev.map((person) => (person.id === id ? updatedPerson : person))
          );
          return updatedPerson;
        } else {
          throw new Error("Person not found");
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to update person";
        setError(errorMessage);
        console.error("Error updating person:", err);
        throw new Error(errorMessage);
      }
    },
    []
  );

  // Delete a person
  const deletePerson = useCallback(async (id: number) => {
    try {
      setError(null);
      const success = await databaseService.deletePerson(id);

      if (success) {
        setPersons((prev) => prev.filter((person) => person.id !== id));
        return true;
      } else {
        throw new Error("Person not found");
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to delete person";
      setError(errorMessage);
      console.error("Error deleting person:", err);
      throw new Error(errorMessage);
    }
  }, []);

  // Get person by ID
  const getPersonById = useCallback(
    (id: number): Person | undefined => {
      return persons.find((person) => person.id === id);
    },
    [persons]
  );

  // Initialize on mount
  useEffect(() => {
    initializeAndLoad();
  }, [initializeAndLoad]);

  return {
    persons,
    loading,
    error,
    createPerson,
    updatePerson,
    deletePerson,
    getPersonById,
    loadPersons,
    clearError: () => setError(null),
  };
};
