import initSqlJs, { Database } from "sql.js";

export interface Person {
  id?: number;
  name: string;
  age: number;
}

const DB_STORAGE_KEY = "sqlite_persons_db";

class DatabaseService {
  private db: Database | null = null;
  private isInitialized = false;
  private SQL: any = null;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize SQL.js
      this.SQL = await initSqlJs({
        // You can provide a custom wasm file URL if needed
        locateFile: (file: string) => `https://sql.js.org/dist/${file}`,
      });

      // Try to load existing database from localStorage
      await this.loadFromStorage();

      // If no existing database, create a new one
      if (!this.db) {
        this.db = new this.SQL.Database();
        this.createTable();
      }

      this.isInitialized = true;
      console.log("Database initialized successfully");
    } catch (error) {
      console.error("Failed to initialize database:", error);
      throw error;
    }
  }

  private async loadFromStorage(): Promise<void> {
    try {
      const savedData = localStorage.getItem(DB_STORAGE_KEY);
      if (savedData && this.SQL) {
        const uint8Array = new Uint8Array(JSON.parse(savedData));
        this.db = new this.SQL.Database(uint8Array);
        console.log("Database loaded from localStorage");
      }
    } catch (error) {
      console.warn("Failed to load database from storage:", error);
      // Continue with new database
    }
  }

  private saveToStorage(): void {
    try {
      if (this.db) {
        const data = this.db.export();
        localStorage.setItem(DB_STORAGE_KEY, JSON.stringify(Array.from(data)));
      }
    } catch (error) {
      console.warn("Failed to save database to storage:", error);
    }
  }

  private createTable(): void {
    if (!this.db) throw new Error("Database not initialized");

    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS persons (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        age INTEGER NOT NULL
      )
    `;

    this.db.run(createTableSQL);
  }

  // CREATE - Add a new person
  async createPerson(person: Omit<Person, "id">): Promise<Person> {
    if (!this.db) throw new Error("Database not initialized");

    const insertSQL = `
      INSERT INTO persons (name, age)
      VALUES (?, ?)
    `;

    this.db.run(insertSQL, [person.name, person.age]);

    // Get the last inserted ID
    const getLastIdSQL = "SELECT last_insert_rowid() as id";
    const result = this.db.exec(getLastIdSQL);
    const id = result[0].values[0][0] as number;

    // Save to localStorage
    this.saveToStorage();

    return {
      id,
      name: person.name,
      age: person.age,
    };
  }

  // READ - Get all persons
  async getAllPersons(): Promise<Person[]> {
    if (!this.db) throw new Error("Database not initialized");

    const selectSQL = "SELECT * FROM persons ORDER BY id";
    const result = this.db.exec(selectSQL);

    if (result.length === 0) return [];

    const persons: Person[] = [];
    const rows = result[0].values;

    for (const row of rows) {
      persons.push({
        id: row[0] as number,
        name: row[1] as string,
        age: row[2] as number,
      });
    }

    return persons;
  }

  // READ - Get person by ID
  async getPersonById(id: number): Promise<Person | null> {
    if (!this.db) throw new Error("Database not initialized");

    const selectSQL = "SELECT * FROM persons WHERE id = ?";
    const result = this.db.exec(selectSQL, [id]);

    if (result.length === 0 || result[0].values.length === 0) {
      return null;
    }

    const row = result[0].values[0];
    return {
      id: row[0] as number,
      name: row[1] as string,
      age: row[2] as number,
    };
  }

  // UPDATE - Update a person
  async updatePerson(
    id: number,
    person: Omit<Person, "id">
  ): Promise<Person | null> {
    if (!this.db) throw new Error("Database not initialized");

    // First check if person exists
    const existingPerson = await this.getPersonById(id);
    if (!existingPerson) {
      return null;
    }

    const updateSQL = `
      UPDATE persons
      SET name = ?, age = ?
      WHERE id = ?
    `;

    this.db.run(updateSQL, [person.name, person.age, id]);

    // Save to localStorage
    this.saveToStorage();

    return {
      id,
      name: person.name,
      age: person.age,
    };
  }

  // DELETE - Delete a person
  async deletePerson(id: number): Promise<boolean> {
    if (!this.db) throw new Error("Database not initialized");

    // First check if person exists
    const existingPerson = await this.getPersonById(id);
    if (!existingPerson) {
      return false;
    }

    const deleteSQL = "DELETE FROM persons WHERE id = ?";
    this.db.run(deleteSQL, [id]);

    // Save to localStorage
    this.saveToStorage();

    return true;
  }

  // Utility method to get database statistics
  async getStats(): Promise<{ totalPersons: number }> {
    if (!this.db) throw new Error("Database not initialized");

    const countSQL = "SELECT COUNT(*) as count FROM persons";
    const result = this.db.exec(countSQL);

    const count = result.length > 0 ? (result[0].values[0][0] as number) : 0;

    return { totalPersons: count };
  }

  // Export database as Uint8Array (for saving to file)
  exportDatabase(): Uint8Array | null {
    if (!this.db) return null;
    return this.db.export();
  }

  // Import database from Uint8Array
  async importDatabase(data: Uint8Array): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.db) throw new Error("Database not initialized");

    const SQL = await initSqlJs({
      locateFile: (file: string) => `https://sql.js.org/dist/${file}`,
    });

    this.db = new SQL.Database(data);
    this.saveToStorage();
  }

  // Clear all data
  async clearAllData(): Promise<void> {
    if (!this.db) throw new Error("Database not initialized");

    const deleteAllSQL = "DELETE FROM persons";
    this.db.run(deleteAllSQL);

    // Save to localStorage
    this.saveToStorage();
  }

  // Clear localStorage
  clearStorage(): void {
    localStorage.removeItem(DB_STORAGE_KEY);
  }
}

// Create a singleton instance
export const databaseService = new DatabaseService();
