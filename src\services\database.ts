import initSqlJs, { Database } from 'sql.js';

export interface Person {
  id?: number;
  name: string;
  age: number;
}

class DatabaseService {
  private db: Database | null = null;
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize SQL.js
      const SQL = await initSqlJs({
        // You can provide a custom wasm file URL if needed
        locateFile: (file: string) => `https://sql.js.org/dist/${file}`
      });

      // Create a new database
      this.db = new SQL.Database();

      // Create the persons table
      this.createTable();
      
      this.isInitialized = true;
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private createTable(): void {
    if (!this.db) throw new Error('Database not initialized');

    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS persons (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        age INTEGER NOT NULL
      )
    `;

    this.db.run(createTableSQL);
  }

  // CREATE - Add a new person
  async create<PERSON>erson(person: Omit<Person, 'id'>): Promise<Person> {
    if (!this.db) throw new Error('Database not initialized');

    const insertSQL = `
      INSERT INTO persons (name, age) 
      VALUES (?, ?)
    `;

    const result = this.db.run(insertSQL, [person.name, person.age]);
    
    return {
      id: result.lastInsertRowid as number,
      name: person.name,
      age: person.age
    };
  }

  // READ - Get all persons
  async getAllPersons(): Promise<Person[]> {
    if (!this.db) throw new Error('Database not initialized');

    const selectSQL = 'SELECT * FROM persons ORDER BY id';
    const result = this.db.exec(selectSQL);

    if (result.length === 0) return [];

    const persons: Person[] = [];
    const rows = result[0].values;

    for (const row of rows) {
      persons.push({
        id: row[0] as number,
        name: row[1] as string,
        age: row[2] as number
      });
    }

    return persons;
  }

  // READ - Get person by ID
  async getPersonById(id: number): Promise<Person | null> {
    if (!this.db) throw new Error('Database not initialized');

    const selectSQL = 'SELECT * FROM persons WHERE id = ?';
    const result = this.db.exec(selectSQL, [id]);

    if (result.length === 0 || result[0].values.length === 0) {
      return null;
    }

    const row = result[0].values[0];
    return {
      id: row[0] as number,
      name: row[1] as string,
      age: row[2] as number
    };
  }

  // UPDATE - Update a person
  async updatePerson(id: number, person: Omit<Person, 'id'>): Promise<Person | null> {
    if (!this.db) throw new Error('Database not initialized');

    const updateSQL = `
      UPDATE persons 
      SET name = ?, age = ? 
      WHERE id = ?
    `;

    const result = this.db.run(updateSQL, [person.name, person.age, id]);

    if (result.changes === 0) {
      return null; // Person not found
    }

    return {
      id,
      name: person.name,
      age: person.age
    };
  }

  // DELETE - Delete a person
  async deletePerson(id: number): Promise<boolean> {
    if (!this.db) throw new Error('Database not initialized');

    const deleteSQL = 'DELETE FROM persons WHERE id = ?';
    const result = this.db.run(deleteSQL, [id]);

    return result.changes > 0;
  }

  // Utility method to get database statistics
  async getStats(): Promise<{ totalPersons: number }> {
    if (!this.db) throw new Error('Database not initialized');

    const countSQL = 'SELECT COUNT(*) as count FROM persons';
    const result = this.db.exec(countSQL);

    const count = result.length > 0 ? result[0].values[0][0] as number : 0;

    return { totalPersons: count };
  }

  // Export database as Uint8Array (for saving to file)
  exportDatabase(): Uint8Array | null {
    if (!this.db) return null;
    return this.db.export();
  }

  // Import database from Uint8Array
  async importDatabase(data: Uint8Array): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.db) throw new Error('Database not initialized');

    const SQL = await initSqlJs({
      locateFile: (file: string) => `https://sql.js.org/dist/${file}`
    });

    this.db = new SQL.Database(data);
  }
}

// Create a singleton instance
export const databaseService = new DatabaseService();
